
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';

interface Tour {
  id?: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  location: string;
  destinations: string[];
  activities: string[];
  accommodations: string[];
  maxGroupSize: number;
  minGroupSize: number;
  difficulty: string;
  includes: string[];
  excludes: string[];
  images: string[];
  featured: boolean;
  status: string;
  rating: number;
  reviewCount: number;
  tourType: string;
  seasonality: {
    greenSeason: boolean;
    drySeason: boolean;
    bestMonths: string[];
  };
  specialFeatures: string[];
  difficultyDetails: string;
}

const TourManagement = () => {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingTour, setEditingTour] = useState<Tour | null>(null);
  const [showForm, setShowForm] = useState(false);
  const { toast } = useToast();

  const initialTourState: Tour = {
    title: '',
    description: '',
    price: 0,
    duration: '',
    location: '',
    destinations: [],
    activities: [],
    accommodations: [],
    maxGroupSize: 10,
    minGroupSize: 2,
    difficulty: 'moderate',
    includes: [],
    excludes: [],
    images: [],
    featured: false,
    status: 'active',
    rating: 0,
    reviewCount: 0,
    tourType: 'standard',
    seasonality: {
      greenSeason: true,
      drySeason: true,
      bestMonths: []
    },
    specialFeatures: [],
    difficultyDetails: ''
  };

  const [formData, setFormData] = useState<Tour>(initialTourState);

  useEffect(() => {
    fetchTours();
  }, []);

  const fetchTours = async () => {
    setLoading(true);
    try {
      const querySnapshot = await getDocs(collection(db, 'tours'));
      const toursData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Tour[];
      setTours(toursData);
    } catch (error) {
      console.error('Error fetching tours:', error);
      toast({
        title: "Error",
        description: "Failed to fetch tours",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const tourData = {
        ...formData,
        updatedAt: Timestamp.now(),
        ...(editingTour ? {} : { createdAt: Timestamp.now() })
      };

      if (editingTour && editingTour.id) {
        await updateDoc(doc(db, 'tours', editingTour.id), tourData);
        toast({
          title: "Success",
          description: "Tour updated successfully",
        });
      } else {
        await addDoc(collection(db, 'tours'), tourData);
        toast({
          title: "Success",
          description: "Tour created successfully",
        });
      }

      setShowForm(false);
      setEditingTour(null);
      setFormData(initialTourState);
      fetchTours();
    } catch (error) {
      console.error('Error saving tour:', error);
      toast({
        title: "Error",
        description: "Failed to save tour",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (tourId: string) => {
    if (!confirm('Are you sure you want to delete this tour?')) return;
    
    setLoading(true);
    try {
      await deleteDoc(doc(db, 'tours', tourId));
      toast({
        title: "Success",
        description: "Tour deleted successfully",
      });
      fetchTours();
    } catch (error) {
      console.error('Error deleting tour:', error);
      toast({
        title: "Error",
        description: "Failed to delete tour",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (tour: Tour) => {
    setEditingTour(tour);
    setFormData(tour);
    setShowForm(true);
  };

  const handleArrayInput = (field: keyof Tour, value: string) => {
    const arrayValue = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({ ...prev, [field]: arrayValue }));
  };

  if (showForm) {
    return (
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>{editingTour ? 'Edit Tour' : 'Create New Tour'}</CardTitle>
          <CardDescription>
            Fill out all the details for the safari tour
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="seasonality">Seasonality</TabsTrigger>
              <TabsTrigger value="images">Images & Features</TabsTrigger>
            </TabsList>
            
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Tour Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Serengeti Wildlife Safari"
                  />
                </div>
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    placeholder="Serengeti National Park, Tanzania"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Experience the Great Migration and witness the Big Five..."
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="price">Price ($)</Label>
                  <Input
                    id="price"
                    type="number"
                    value={formData.price}
                    onChange={(e) => setFormData(prev => ({ ...prev, price: Number(e.target.value) }))}
                  />
                </div>
                <div>
                  <Label htmlFor="duration">Duration</Label>
                  <Input
                    id="duration"
                    value={formData.duration}
                    onChange={(e) => setFormData(prev => ({ ...prev, duration: e.target.value }))}
                    placeholder="7 days"
                  />
                </div>
                <div>
                  <Label htmlFor="tourType">Tour Type</Label>
                  <Select value={formData.tourType} onValueChange={(value) => setFormData(prev => ({ ...prev, tourType: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="standard">Standard</SelectItem>
                      <SelectItem value="luxury">Luxury</SelectItem>
                      <SelectItem value="budget">Budget</SelectItem>
                      <SelectItem value="private">Private</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="minGroupSize">Min Group Size</Label>
                  <Input
                    id="minGroupSize"
                    type="number"
                    value={formData.minGroupSize}
                    onChange={(e) => setFormData(prev => ({ ...prev, minGroupSize: Number(e.target.value) }))}
                  />
                </div>
                <div>
                  <Label htmlFor="maxGroupSize">Max Group Size</Label>
                  <Input
                    id="maxGroupSize"
                    type="number"
                    value={formData.maxGroupSize}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxGroupSize: Number(e.target.value) }))}
                  />
                </div>
                <div>
                  <Label htmlFor="difficulty">Difficulty</Label>
                  <Select value={formData.difficulty} onValueChange={(value) => setFormData(prev => ({ ...prev, difficulty: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easy">Easy</SelectItem>
                      <SelectItem value="moderate">Moderate</SelectItem>
                      <SelectItem value="challenging">Challenging</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <div>
                <Label htmlFor="destinations">Destinations (comma-separated)</Label>
                <Input
                  id="destinations"
                  value={formData.destinations.join(', ')}
                  onChange={(e) => handleArrayInput('destinations', e.target.value)}
                  placeholder="Serengeti, Ngorongoro Crater, Tarangire"
                />
              </div>

              <div>
                <Label htmlFor="activities">Activities (comma-separated)</Label>
                <Input
                  id="activities"
                  value={formData.activities.join(', ')}
                  onChange={(e) => handleArrayInput('activities', e.target.value)}
                  placeholder="Game Drives, Wildlife Photography, Cultural Visits"
                />
              </div>

              <div>
                <Label htmlFor="accommodations">Accommodations (comma-separated)</Label>
                <Input
                  id="accommodations"
                  value={formData.accommodations.join(', ')}
                  onChange={(e) => handleArrayInput('accommodations', e.target.value)}
                  placeholder="Safari Lodge, Luxury Tented Camp"
                />
              </div>

              <div>
                <Label htmlFor="includes">What's Included (comma-separated)</Label>
                <Textarea
                  id="includes"
                  value={formData.includes.join(', ')}
                  onChange={(e) => handleArrayInput('includes', e.target.value)}
                  placeholder="Accommodation, All Meals, Park Fees, Professional Guide"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="excludes">What's Excluded (comma-separated)</Label>
                <Textarea
                  id="excludes"
                  value={formData.excludes.join(', ')}
                  onChange={(e) => handleArrayInput('excludes', e.target.value)}
                  placeholder="International Flights, Visa Fees, Personal Expenses"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="difficultyDetails">Difficulty Details</Label>
                <Textarea
                  id="difficultyDetails"
                  value={formData.difficultyDetails}
                  onChange={(e) => setFormData(prev => ({ ...prev, difficultyDetails: e.target.value }))}
                  placeholder="Suitable for most fitness levels with minimal walking required"
                  rows={2}
                />
              </div>
            </TabsContent>

            <TabsContent value="seasonality" className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.seasonality.greenSeason}
                    onCheckedChange={(checked) => setFormData(prev => ({
                      ...prev,
                      seasonality: { ...prev.seasonality, greenSeason: checked }
                    }))}
                  />
                  <Label>Available in Green Season</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.seasonality.drySeason}
                    onCheckedChange={(checked) => setFormData(prev => ({
                      ...prev,
                      seasonality: { ...prev.seasonality, drySeason: checked }
                    }))}
                  />
                  <Label>Available in Dry Season</Label>
                </div>
              </div>

              <div>
                <Label htmlFor="bestMonths">Best Months to Visit (comma-separated)</Label>
                <Input
                  id="bestMonths"
                  value={formData.seasonality.bestMonths.join(', ')}
                  onChange={(e) => {
                    const months = e.target.value.split(',').map(month => month.trim()).filter(month => month);
                    setFormData(prev => ({
                      ...prev,
                      seasonality: { ...prev.seasonality, bestMonths: months }
                    }));
                  }}
                  placeholder="June, July, August, September, October"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={formData.featured}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: checked }))}
                />
                <Label>Featured Tour</Label>
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="images" className="space-y-4">
              <div>
                <Label htmlFor="images">Image URLs (comma-separated)</Label>
                <Textarea
                  id="images"
                  value={formData.images.join(', ')}
                  onChange={(e) => handleArrayInput('images', e.target.value)}
                  placeholder="https://images.unsplash.com/photo-1516426122078-c23e76319801"
                  rows={4}
                />
              </div>

              <div>
                <Label htmlFor="specialFeatures">Special Features (comma-separated)</Label>
                <Input
                  id="specialFeatures"
                  value={formData.specialFeatures.join(', ')}
                  onChange={(e) => handleArrayInput('specialFeatures', e.target.value)}
                  placeholder="Big Five Viewing, Great Migration, Cultural Experience"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="rating">Rating (0-5)</Label>
                  <Input
                    id="rating"
                    type="number"
                    min="0"
                    max="5"
                    step="0.1"
                    value={formData.rating}
                    onChange={(e) => setFormData(prev => ({ ...prev, rating: Number(e.target.value) }))}
                  />
                </div>
                <div>
                  <Label htmlFor="reviewCount">Review Count</Label>
                  <Input
                    id="reviewCount"
                    type="number"
                    value={formData.reviewCount}
                    onChange={(e) => setFormData(prev => ({ ...prev, reviewCount: Number(e.target.value) }))}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setShowForm(false);
                setEditingTour(null);
                setFormData(initialTourState);
              }}
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={loading}>
              <Save className="w-4 h-4 mr-2" />
              {loading ? 'Saving...' : 'Save Tour'}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Tour Management</CardTitle>
            <CardDescription>Manage safari tours and experiences</CardDescription>
          </div>
          <Button onClick={() => setShowForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add New Tour
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">Loading tours...</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Featured</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tours.map((tour) => (
                <TableRow key={tour.id}>
                  <TableCell className="font-medium">{tour.title}</TableCell>
                  <TableCell>{tour.location}</TableCell>
                  <TableCell>${tour.price}</TableCell>
                  <TableCell>{tour.duration}</TableCell>
                  <TableCell>
                    <Badge variant={tour.status === 'active' ? 'default' : 'secondary'}>
                      {tour.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {tour.featured && <Badge variant="outline">Featured</Badge>}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(tour)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => tour.id && handleDelete(tour.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};

export default TourManagement;
