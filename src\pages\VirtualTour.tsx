
import React, { useState, useRef, useEffect } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, RotateCcw, Maximize, Volume2, VolumeX, Camera, Info, MapPin } from 'lucide-react';

const VirtualTour = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentScene, setCurrentScene] = useState(0);
  const [showInfo, setShowInfo] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const scenes = [
    {
      id: 0,
      title: 'Serengeti Plains',
      description: 'Experience the vast grasslands where millions of wildebeest roam during the Great Migration.',
      image: 'https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=1920&h=1080',
      video: 'https://sample-videos.com/zip/10/mp4/480p/SampleVideo_1280x720_1mb.mp4',
      hotspots: [
        { x: 30, y: 60, title: 'Wildebeest Herd', description: 'Large herd moving towards water' },
        { x: 70, y: 40, title: 'Acacia Tree', description: 'Classic African savanna tree' }
      ]
    },
    {
      id: 1,
      title: 'Ngorongoro Crater',
      description: 'Descend into the world\'s largest intact volcanic caldera, home to over 25,000 large animals.',
      image: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?auto=format&fit=crop&w=1920&h=1080',
      video: 'https://sample-videos.com/zip/10/mp4/480p/SampleVideo_1280x720_2mb.mp4',
      hotspots: [
        { x: 50, y: 70, title: 'Crater Floor', description: 'Rich grasslands teeming with wildlife' },
        { x: 80, y: 30, title: 'Flamingo Lake', description: 'Seasonal lake attracting thousands of flamingos' }
      ]
    },
    {
      id: 2,
      title: 'Maasai Village',
      description: 'Visit a traditional Maasai community and learn about their ancient culture and customs.',
      image: 'https://images.unsplash.com/photo-1493962853295-0fd70327578a?auto=format&fit=crop&w=1920&h=1080',
      video: 'https://sample-videos.com/zip/10/mp4/480p/SampleVideo_1280x720_1mb.mp4',
      hotspots: [
        { x: 40, y: 50, title: 'Traditional Huts', description: 'Mud and dung construction' },
        { x: 65, y: 65, title: 'Cattle Enclosure', description: 'Center of Maasai life' }
      ]
    }
  ];

  const currentSceneData = scenes[currentScene];

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const resetView = () => {
    // In a real 360° viewer, this would reset the camera angle
    setShowInfo(false);
  };

  const enterFullscreen = () => {
    if (videoRef.current) {
      videoRef.current.requestFullscreen();
    }
  };

  const takeScreenshot = () => {
    // In a real implementation, this would capture the current 360° view
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx && videoRef.current) {
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      ctx.drawImage(videoRef.current, 0, 0);
      
      const link = document.createElement('a');
      link.download = `safari-screenshot-${Date.now()}.png`;
      link.href = canvas.toDataURL();
      link.click();
    }
  };

  return (
    <div className="min-h-screen bg-black">
      <Header />
      <main className="pt-16">
        {/* Virtual Tour Player */}
        <div className="relative h-screen bg-black">
          {/* Main 360° View */}
          <div className="relative w-full h-full">
            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              poster={currentSceneData.image}
              loop
              playsInline
            >
              <source src={currentSceneData.video} type="video/mp4" />
              Your browser does not support the video tag.
            </video>

            {/* Hotspots */}
            {currentSceneData.hotspots.map((hotspot, index) => (
              <button
                key={index}
                className="absolute w-6 h-6 bg-orange-600 rounded-full border-2 border-white animate-pulse hover:scale-110 transition-transform"
                style={{ left: `${hotspot.x}%`, top: `${hotspot.y}%` }}
                onClick={() => setShowInfo(true)}
              >
                <Info className="w-3 h-3 text-white mx-auto" />
              </button>
            ))}

            {/* Scene Info Overlay */}
            {showInfo && (
              <div className="absolute top-4 left-4 right-4 bg-black/80 text-white p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-2">{currentSceneData.title}</h3>
                <p className="text-sm opacity-90 mb-3">{currentSceneData.description}</p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowInfo(false)}
                  className="text-white hover:bg-white/20"
                >
                  Close
                </Button>
              </div>
            )}

            {/* Control Bar */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
              <div className="flex items-center justify-between text-white">
                <div className="flex items-center space-x-4">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={togglePlay}
                    className="text-white hover:bg-white/20"
                  >
                    {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleMute}
                    className="text-white hover:bg-white/20"
                  >
                    {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                  </Button>

                  <div className="text-sm">
                    <Badge variant="outline" className="text-white border-white/30">
                      {currentSceneData.title}
                    </Badge>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={resetView}
                    className="text-white hover:bg-white/20"
                    title="Reset View"
                  >
                    <RotateCcw className="w-5 h-5" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={takeScreenshot}
                    className="text-white hover:bg-white/20"
                    title="Take Screenshot"
                  >
                    <Camera className="w-5 h-5" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowInfo(!showInfo)}
                    className="text-white hover:bg-white/20"
                    title="Show Info"
                  >
                    <Info className="w-5 h-5" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={enterFullscreen}
                    className="text-white hover:bg-white/20"
                    title="Fullscreen"
                  >
                    <Maximize className="w-5 h-5" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Scene Navigation */}
          <div className="absolute top-4 right-4 space-y-2">
            {scenes.map((scene, index) => (
              <button
                key={scene.id}
                className={`block w-20 h-12 rounded-lg overflow-hidden border-2 transition-all ${
                  currentScene === index ? 'border-orange-600' : 'border-white/30'
                }`}
                onClick={() => setCurrentScene(index)}
              >
                <img
                  src={scene.image}
                  alt={scene.title}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        </div>

        {/* Tour Information */}
        <div className="bg-white py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <Badge className="mb-4 bg-orange-600 text-white px-4 py-2">
                  Virtual Safari Experience
                </Badge>
                <h1 className="text-3xl md:text-4xl font-bold mb-4">
                  Explore Tanzania from Anywhere
                </h1>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Experience the magic of an African safari through our immersive virtual tours. 
                  Navigate through 360° views of iconic locations and wildlife encounters.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {scenes.map((scene, index) => (
                  <Card key={scene.id} className="overflow-hidden">
                    <div className="relative h-48">
                      <img
                        src={scene.image}
                        alt={scene.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => setCurrentScene(index)}
                          className="text-white bg-white/20 hover:bg-white/30"
                        >
                          <Play className="w-6 h-6" />
                        </Button>
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold text-lg mb-2">{scene.title}</h3>
                      <p className="text-gray-600 text-sm">{scene.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="mt-12 text-center">
                <h2 className="text-2xl font-bold mb-4">Ready for the Real Adventure?</h2>
                <p className="text-gray-600 mb-6">
                  Book your actual safari tour and experience these incredible destinations in person.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="bg-orange-600 hover:bg-orange-700">
                    Book Your Safari
                  </Button>
                  <Button variant="outline" size="lg">
                    View All Tours
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="bg-gray-50 py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-center mb-12">Virtual Tour Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <RotateCcw className="w-8 h-8 text-orange-600" />
                  </div>
                  <h3 className="font-semibold mb-2">360° Views</h3>
                  <p className="text-sm text-gray-600">Immersive panoramic views of safari destinations</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Info className="w-8 h-8 text-orange-600" />
                  </div>
                  <h3 className="font-semibold mb-2">Interactive Hotspots</h3>
                  <p className="text-sm text-gray-600">Click on points of interest for detailed information</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Camera className="w-8 h-8 text-orange-600" />
                  </div>
                  <h3 className="font-semibold mb-2">Screenshots</h3>
                  <p className="text-sm text-gray-600">Capture and save your favorite virtual moments</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MapPin className="w-8 h-8 text-orange-600" />
                  </div>
                  <h3 className="font-semibold mb-2">Multiple Locations</h3>
                  <p className="text-sm text-gray-600">Explore various safari destinations virtually</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default VirtualTour;
