
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from '@/contexts/AuthContext';
import { LanguageProvider } from '@/contexts/LanguageContext';
import { WishlistProvider } from '@/contexts/WishlistContext';
import { Toaster } from '@/components/ui/toaster';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

// Pages
import Index from '@/pages/Index';
import Tours from '@/pages/Tours';
import TourDetail from '@/pages/TourDetail';
import About from '@/pages/About';
import Contact from '@/pages/Contact';
import Gallery from '@/pages/Gallery';
import Blog from '@/pages/Blog';
import BlogPost from '@/pages/BlogPost';
import Reviews from '@/pages/Reviews';
import UserDashboard from '@/pages/UserDashboard';
import AdminDashboard from '@/pages/admin/AdminDashboard';
import Booking from '@/pages/Booking';
import TourBuilder from '@/pages/TourBuilder';
import Register from '@/pages/Register';
import Login from '@/pages/Login';
import NotFound from '@/pages/NotFound';
import VirtualTourPage from '@/pages/VirtualTourPage';
import VirtualTour from '@/pages/VirtualTour';
import TravelResources from '@/pages/TravelResources';
import EnhancedTours from '@/pages/EnhancedTours';
import Privacy from '@/pages/Privacy';
import Terms from '@/pages/Terms';
import Cookies from '@/pages/Cookies';


const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <LanguageProvider>
        <WishlistProvider>
          <AuthProvider>
            <Router>
              <div className="min-h-screen bg-background">
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/tours" element={<Tours />} />
                  <Route path="/tours/:id" element={<TourDetail />} />
                  <Route path="/book/:tourId" element={<Booking />} />
                  <Route path="/tour-builder" element={<TourBuilder />} />
                  <Route path="/about" element={<About />} />
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/gallery" element={<Gallery />} />
                  <Route path="/blog" element={<Blog />} />
                  <Route path="/blog/:slug" element={<BlogPost />} />
                  <Route path="/reviews" element={<Reviews />} />
                  <Route path="/user-dashboard" element={
                    <ProtectedRoute>
                      <UserDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/*" element={
                    <ProtectedRoute adminOnly={true}>
                      <AdminDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/register" element={<Register />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/virtual-tour" element={<VirtualTourPage />} />
                  <Route path="/virtual-tour/:destination" element={<VirtualTour />} />
                  <Route path="/travel-resources" element={<TravelResources />} />
                  <Route path="/enhanced-tours" element={<EnhancedTours />} />
                  <Route path="/privacy" element={<Privacy />} />
                  <Route path="/terms" element={<Terms />} />
                  <Route path="/cookies" element={<Cookies />} />
                  <Route path="/dashboard" element={
                    <ProtectedRoute>
                      <UserDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="*" element={<NotFound />} />
                </Routes>
                <Toaster />
              </div>
            </Router>
          </AuthProvider>
        </WishlistProvider>
      </LanguageProvider>
    </QueryClientProvider>
  );
}

export default App;
